{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\VirtualTryOn.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\n\n// Add CSS for range slider styling\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\nconst Tryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkMobile();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,\n    // mm - default men's wrist size\n    women: 54 // mm - default women's wrist size\n  };\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio,\n      // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"imgs/watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"imgs/watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"imgs/watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"imgs/watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"imgs/watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"imgs/watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"imgs/bracelets_tryon/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"imgs/bracelets_tryon/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"imgs/bracelets_tryon/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"imgs/bracelets_tryon/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"imgs/bracelets_tryon/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"imgs/bracelets_tryon/bracelet_6.png\"\n  }];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = function () {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = r > 245 && g > 245 && b > 245 && Math.abs(r - g) < 10 && Math.abs(g - b) < 10 && !isNearEdge;\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n    img.onerror = function () {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n\n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n\n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n\n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Update product selection panel JSX\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.cameraContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        style: styles.cameraFeed,\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        style: styles.capturedImage,\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.homeBtn,\n        onClick: onBackToHome,\n        \"aria-label\": \"Home\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: isMobile ? 10 : 20,\n          right: isMobile ? 10 : 20,\n          zIndex: 20,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: '#2D8C88',\n            fontWeight: 700,\n            fontSize: isMobile ? 14 : 16,\n            marginBottom: 6,\n            letterSpacing: 0.5\n          },\n          children: \"Auto Capture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"slider\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"circle\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"checkmark\",\n                viewBox: \"0 0 12 10\",\n                children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"1.5 6 5 9 10.5 1\",\n                  fill: \"none\",\n                  stroke: \"#2D8C88\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"cross\",\n                viewBox: \"0 0 10 10\",\n                children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"1\",\n                  y1: \"1\",\n                  x2: \"9\",\n                  y2: \"9\",\n                  stroke: \"#838383\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 62\n                }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: \"9\",\n                  y1: \"1\",\n                  x2: \"1\",\n                  y2: \"9\",\n                  stroke: \"#838383\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 152\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.backBtn,\n        onClick: handleBackWithReset,\n        \"aria-label\": \"Back\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.countdownDisplay,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownNumber,\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownText,\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusText,\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusSubtext,\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 903,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.statusText,\n            backgroundColor: 'rgba(45, 140, 136, 0.9)'\n          },\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1009,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 919,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n        style: {\n          position: 'absolute',\n          width: 0,\n          height: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"defs\", {\n          children: /*#__PURE__*/_jsxDEV(\"clipPath\", {\n            id: \"wrist-clip-path\",\n            children: /*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"50%\",\n              cy: \"50%\",\n              r: \"7.5%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 9\n      }, this), selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.productPosition,\n          width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n          height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`,\n          // Apply clipping only when watch is scaled larger than default\n          clipPath: activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZES[userGender] * 0.8 ? 'url(#wrist-clip-path)' : 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n            alt: \"Selected product\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              transform: activeTab === 'Bracelets' ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}` : `scale(${WATCH_HEIGHT / 25 * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n            },\n            onLoad: e => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1052,\n            columnNumber: 15\n          }, this), activeTab === 'Watches' && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.dialSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8,\n                marginLeft: '4px'\n              },\n              children: [\"(scaled \", ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1085,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1035,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.captureBtn,\n        className: isMobile ? 'mobile-capture-btn' : '',\n        onClick: handleCapture,\n        \"aria-label\": isCaptured ? \"Select Products\" : \"Capture\",\n        children: !isCaptured ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureInner,\n          className: isMobile ? 'mobile-inner-circle' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1107,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1109,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 9\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.resetBtn,\n        onClick: () => window.location.reload(),\n        \"aria-label\": \"Reset\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 7\n    }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.wristSizeFloatingBtn,\n      className: isMobile ? 'mobile-btn' : '',\n      onClick: () => setShowWristSizeModal(true),\n      \"aria-label\": \"Adjust wrist size\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.wristSizeText,\n        children: [userWristSize, \"mm\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1131,\n      columnNumber: 9\n    }, this), showWristSizeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalOverlay,\n      onClick: () => setShowWristSizeModal(false),\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeModal,\n        onClick: e => e.stopPropagation(),\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.modalTitle,\n            children: \"Adjust Wrist Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1157,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modalCloseBtn,\n            onClick: () => setShowWristSizeModal(false),\n            \"aria-label\": \"Close\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalContent,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.genderSelection,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...(userGender === 'men' ? styles.genderButtonActive : {})\n              },\n              onClick: () => handleGenderChange('men'),\n              children: \"Men (64mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...(userGender === 'women' ? styles.genderButtonActive : {})\n              },\n              onClick: () => handleGenderChange('women'),\n              children: \"Women (54mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.sliderLabel,\n              children: [\"Wrist Size: \", userWristSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZES[userGender] && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: styles.sizeChange,\n                children: [\"(\", userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : '', ((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0), \"%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"45\",\n              max: \"80\",\n              value: userWristSize,\n              onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n              style: styles.slider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.sliderLabels,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"45mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"80mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.presetButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(50),\n                children: \"Small (50mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender]),\n                children: [\"Default (\", DEFAULT_WRIST_SIZES[userGender], \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(70),\n                children: \"Large (70mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1151,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1146,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: panelRef,\n      style: {\n        ...styles.productSelection,\n        transform: `translateY(${panelPosition}px)`,\n        touchAction: 'none'\n      },\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dragHandle,\n        \"aria-hidden\": \"true\",\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1267,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1266,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1305,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 21\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1294,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1245,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 833,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(Tryon, \"EQyd90FOSd6ufQjNdFvw/BEwAfU=\");\n_c = Tryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px',\n    // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  }\n};\nexport default Tryon;\nvar _c;\n$RefreshReg$(_c, \"Tryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sliderCSS", "document", "getElementById", "styleElement", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeModal", "setShowWristSizeModal", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "panelRef", "checkMobile", "window", "innerWidth", "setVH", "vh", "innerHeight", "documentElement", "style", "setProperty", "addEventListener", "setTimeout", "removeEventListener", "DEFAULT_WRIST_SIZES", "men", "women", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "wristSizeRatio", "mmToSvgScale", "watchWidthSvg", "totalWidth", "watchHeightSvg", "totalHeight", "dialDiameterSvg", "dialDiameter", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "width", "Math", "max", "height", "scale", "min", "realWidth", "realHeight", "caseDiameter", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "type", "watches", "name", "path", "caseThickness", "dialSize", "bracelets", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "console", "error", "src", "display", "log", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "videoWidth", "videoHeight", "ctx", "getContext", "drawImage", "toDataURL", "removeBackground", "imgElement", "productType", "img", "Image", "crossOrigin", "onload", "naturalWidth", "naturalHeight", "imageData", "getImageData", "data", "edgePixels", "Set", "y", "x", "idx", "r", "g", "b", "isEdge", "dy", "dx", "neighborIdx", "nr", "ng", "nb", "colorDiff", "abs", "add", "i", "length", "pixelIndex", "brightness", "isNearEdge", "has", "isPureWhite", "putImageData", "filter", "mixBlendMode", "opacity", "e", "warn", "onerror", "detectHandOrientation", "random", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "handleCapture", "capturedDataUrl", "handleBack", "handleGenderChange", "gender", "handleWristSizeChange", "size", "handleTabChange", "tabName", "handleProductSelect", "product", "interval", "setInterval", "clearInterval", "countdownInterval", "prev", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "handleTouchStart", "touches", "clientY", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "handleClickOutside", "target", "closest", "styles", "container", "children", "cameraContainer", "ref", "cameraFeed", "autoPlay", "playsInline", "muted", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "capturedImage", "alt", "homeBtn", "onClick", "position", "top", "right", "zIndex", "flexDirection", "alignItems", "color", "fontWeight", "fontSize", "marginBottom", "letterSpacing", "className", "checked", "onChange", "disabled", "viewBox", "points", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "backBtn", "countdownDisplay", "countdownNumber", "countdownText", "statusMessage", "statusText", "statusSubtext", "backgroundColor", "handGuide", "xmlns", "d", "rx", "cx", "cy", "textAnchor", "productPosition", "clipPath", "justifyContent", "objectFit", "transform", "onLoad", "bottom", "left", "padding", "borderRadius", "whiteSpace", "pointerEvents", "boxShadow", "marginLeft", "toFixed", "captureBtn", "captureInner", "resetBtn", "location", "reload", "wristSizeFloatingBtn", "wristSizeText", "modalOverlay", "wristSizeModal", "stopPropagation", "modalHeader", "modalTitle", "modalCloseBtn", "modalContent", "genderSelection", "genderButton", "genderButtonActive", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "sizeChange", "value", "parseInt", "slider", "slider<PERSON><PERSON><PERSON>", "presetButtons", "presetButton", "productSelection", "touchAction", "role", "onTouchStart", "onTouchMove", "onTouchEnd", "dragHandle", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "title", "productImage", "onError", "productLabel", "productName", "productSize", "_c", "fontFamily", "overflow", "WebkitTapHighlightColor", "WebkitOverflowScrolling", "flex", "WebkitTransform", "cursor", "border", "transition", "outline", "switchContainer", "gap", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "textAlign", "animation", "max<PERSON><PERSON><PERSON>", "WebkitFilter", "min<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "borderBottom", "overflowY", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "lineHeight", "background", "WebkitAppearance", "appearance", "continueButton", "borderTopLeftRadius", "borderTopRightRadius", "<PERSON><PERSON><PERSON><PERSON>", "userSelect", "WebkitUserSelect", "closeBtn", "gridTemplateColumns", "paddingBottom", "scrollbarWidth", "scrollbarColor", "aspectRatio", "textOverflow", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/VirtualTryOn.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\n// Add CSS for range slider styling\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\n\nconst Tryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkMobile();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkMobile();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkMobile);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Default wrist sizes by gender\n  const DEFAULT_WRIST_SIZES = {\n    men: 64,    // mm - default men's wrist size\n    women: 54   // mm - default women's wrist size\n  };\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Calculate realistic watch sizing based on actual dimensions and user's wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Calculate relative scaling factor based on user's wrist size vs default\n    const wristSizeRatio = userWristSize / defaultWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using user's wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    // SVG circle is at cx=\"400\" cy=\"300\" (center of the hand area)\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio, // Dynamic scale with wrist size adjustment\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio // Include ratio for additional scaling if needed\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY\n    };\n  };\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"imgs/watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"imgs/watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"imgs/watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"imgs/watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"imgs/watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"imgs/watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"imgs/bracelets_tryon/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"imgs/bracelets_tryon/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"imgs/bracelets_tryon/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"imgs/bracelets_tryon/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"imgs/bracelets_tryon/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"imgs/bracelets_tryon/bracelet_6.png\" }\n  ];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = function() {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = (\n            r > 245 &&\n            g > 245 &&\n            b > 245 &&\n            Math.abs(r - g) < 10 &&\n            Math.abs(g - b) < 10 &&\n            !isNearEdge\n          );\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n\n    img.onerror = function() {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40 // Default to 40mm if not found\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender(gender);\n    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserGender('men');\n    setUserWristSize(64);\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = (e) => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n\n  const handleTouchMove = (e) => {\n    if (!isDragging) return;\n    \n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    \n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    \n    setIsDragging(false);\n    \n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    \n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = (e) => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    \n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Update product selection panel JSX\n  return (\n    <div style={styles.container}>\n      <div style={styles.cameraContainer}>\n        <video\n          ref={videoRef}\n          style={styles.cameraFeed}\n          autoPlay\n          playsInline\n          muted\n        />\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        <img\n          ref={capturedImageRef}\n          style={styles.capturedImage}\n          alt=\"Captured hand\"\n        />\n\n        {/* Simple Home Button - Only visible when not captured */}\n        {!isCaptured && (\n          <button\n            style={styles.homeBtn}\n            onClick={onBackToHome}\n            aria-label=\"Home\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Autocapture Switch Button - Only visible when not captured */}\n        {!isCaptured && (\n          <div style={{ position: 'absolute', top: isMobile ? 10 : 20, right: isMobile ? 10 : 20, zIndex: 20, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n            <label style={{ color: '#2D8C88', fontWeight: 700, fontSize: isMobile ? 14 : 16, marginBottom: 6, letterSpacing: 0.5 }}>Auto Capture</label>\n            <label className=\"switch\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n              />\n              <span className=\"slider\">\n                <span className=\"circle\">\n                  <svg className=\"checkmark\" viewBox=\"0 0 12 10\"><polyline points=\"1.5 6 5 9 10.5 1\" fill=\"none\" stroke=\"#2D8C88\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/></svg>\n                  <svg className=\"cross\" viewBox=\"0 0 10 10\"><line x1=\"1\" y1=\"1\" x2=\"9\" y2=\"9\" stroke=\"#838383\" strokeWidth=\"2\" strokeLinecap=\"round\"/><line x1=\"9\" y1=\"1\" x2=\"1\" y2=\"9\" stroke=\"#838383\" strokeWidth=\"2\" strokeLinecap=\"round\"/></svg>\n                </span>\n              </span>\n            </label>\n          </div>\n        )}\n\n        {/* Simple Back Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.backBtn}\n            onClick={handleBackWithReset}\n            aria-label=\"Back\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div style={styles.countdownDisplay}>\n            <div style={styles.countdownNumber}>{countdown}</div>\n            <div style={styles.countdownText}>Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={styles.statusText}>Position your arm and wrist in the guide area</div>\n            <div style={styles.statusSubtext}>Countdown will start automatically when detected</div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* SVG Clipping Mask for Product Display */}\n        <svg style={{ position: 'absolute', width: 0, height: 0 }}>\n          <defs>\n            <clipPath id=\"wrist-clip-path\">\n              {/* Create a clipping path that matches the SVG guide circle */}\n              {/* SVG viewBox is 800x600, circle is at cx=\"400\" cy=\"300\" r=\"60\" */}\n              {/* Convert to percentage: cx=50%, cy=50%, r=7.5% (60/800*100) */}\n              <circle cx=\"50%\" cy=\"50%\" r=\"7.5%\" />\n            </clipPath>\n          </defs>\n        </svg>\n\n        {/* Product Display */}\n        {selectedProduct && (\n          <div style={{\n            ...styles.productPosition,\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`,\n            // Apply clipping only when watch is scaled larger than default\n            clipPath: activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZES[userGender] * 0.8\n              ? 'url(#wrist-clip-path)'\n              : 'none'\n          }}>\n            <div style={{\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <img\n                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                alt=\"Selected product\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain',\n                  transform: activeTab === 'Bracelets'\n                    ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`\n                    : `scale(${(WATCH_HEIGHT / 25) * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,\n                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                }}\n                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}\n              />\n              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.dialSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                    <span style={{\n                      fontSize: '10px',\n                      opacity: 0.8,\n                      marginLeft: '4px'\n                    }}>\n                      (scaled {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Camera-style Capture Button */}\n        <button\n          style={styles.captureBtn}\n          className={isMobile ? 'mobile-capture-btn' : ''}\n          onClick={handleCapture}\n          aria-label={isCaptured ? \"Select Products\" : \"Capture\"}\n        >\n          {!isCaptured ? (\n            <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>\n          ) : (\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"/>\n            </svg>\n          )}\n        </button>\n\n        {/* Reset Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.resetBtn}\n            onClick={() => window.location.reload()}\n            aria-label=\"Reset\"\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"/>\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Mobile Wrist Size Button - Top right corner */}\n      {isCaptured && (\n        <button\n          style={styles.wristSizeFloatingBtn}\n          className={isMobile ? 'mobile-btn' : ''}\n          onClick={() => setShowWristSizeModal(true)}\n          aria-label=\"Adjust wrist size\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"white\">\n            <path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"/>\n          </svg>\n          <span style={styles.wristSizeText}>{userWristSize}mm</span>\n        </button>\n      )}\n\n      {/* Wrist Size Modal - Mobile-friendly popup */}\n      {showWristSizeModal && (\n        <div \n          style={styles.modalOverlay} \n          onClick={() => setShowWristSizeModal(false)}\n          className=\"modal-overlay\"\n        >\n          <div \n            style={styles.wristSizeModal} \n            onClick={(e) => e.stopPropagation()}\n            className=\"modal-content\"\n          >\n            <div style={styles.modalHeader}>\n              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>\n              <button\n                style={styles.modalCloseBtn}\n                onClick={() => setShowWristSizeModal(false)}\n                aria-label=\"Close\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n                </svg>\n              </button>\n            </div>\n\n            <div style={styles.modalContent}>\n              {/* Gender Selection */}\n              <div style={styles.genderSelection}>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...(userGender === 'men' ? styles.genderButtonActive : {})\n                  }}\n                  onClick={() => handleGenderChange('men')}\n                >\n                  Men (64mm)\n                </button>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...(userGender === 'women' ? styles.genderButtonActive : {})\n                  }}\n                  onClick={() => handleGenderChange('women')}\n                >\n                  Women (54mm)\n                </button>\n              </div>\n\n              {/* Wrist Size Slider */}\n              <div style={styles.sliderContainer}>\n                <label style={styles.sliderLabel}>\n                  Wrist Size: {userWristSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (\n                    <span style={styles.sizeChange}>\n                      ({userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : ''}\n                      {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)\n                    </span>\n                  )}\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"45\"\n                  max=\"80\"\n                  value={userWristSize}\n                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                  style={styles.slider}\n                />\n                <div style={styles.sliderLabels}>\n                  <span>45mm</span>\n                  <span>80mm</span>\n                </div>\n\n                {/* Quick Size Presets */}\n                <div style={styles.presetButtons}>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(50)}\n                  >\n                    Small (50mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender])}\n                  >\n                    Default ({DEFAULT_WRIST_SIZES[userGender]}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(70)}\n                  >\n                    Large (70mm)\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          ref={panelRef}\n          style={{\n            ...styles.productSelection,\n            transform: `translateY(${panelPosition}px)`,\n            touchAction: 'none'\n          }}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n        >\n          <div \n            style={styles.dragHandle} \n            aria-hidden=\"true\"\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n          />\n          <div style={styles.productTabs}>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Watches' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Watches')}\n            >\n              Watches\n            </button>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Bracelets')}\n            >\n              Bracelets\n            </button>\n          </div>\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().map((product, index) => {\n              // Simple null check only\n              if (!product) return null;\n\n              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n              return (\n                <button\n                  key={index}\n                  style={{\n                    ...styles.productItem,\n                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                  }}\n                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                  onClick={() => handleProductSelect(product)}\n                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                >\n                  <img\n                    src={product.path}\n                    alt={product.name}\n                    style={styles.productImage}\n                    onError={(e) => {\n                      e.target.parentElement.style.display = 'none';\n                    }}\n                  />\n                  <div style={styles.productLabel}>\n                    <div style={styles.productName}>{product.name}</div>\n                    {activeTab === 'Watches' && product.caseDiameter && (\n                      <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                    )}\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    pointerEvents: 'none',\n    minWidth: '100px', // Ensure minimum size\n    minHeight: '100px' // Ensure minimum size\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  }\n};\n\nexport default Tryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC,EAAE;EAC3F,MAAMC,YAAY,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,EAAE,GAAG,0BAA0B;EAC5CF,YAAY,CAACG,WAAW,GAAGN,SAAS;EACpCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;AACzC;AAEA,MAAMM,KAAK,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClC;EACA,MAAMC,QAAQ,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkB,gBAAgB,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMmB,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACwC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAMwD,QAAQ,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMwD,WAAW,GAAGA,CAAA,KAAM;MACxBtB,WAAW,CAACuB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAGH,MAAM,CAACI,WAAW,GAAG,IAAI;MACpCtD,QAAQ,CAACuD,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,GAAGJ,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDJ,WAAW,CAAC,CAAC;IACbG,KAAK,CAAC,CAAC;IAEPF,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCT,WAAW,CAAC,CAAC;MACbG,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEFF,MAAM,CAACQ,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDC,UAAU,CAAC,MAAM;QACfP,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACXF,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEX,WAAW,CAAC;MACjDC,MAAM,CAACU,mBAAmB,CAAC,mBAAmB,EAAER,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMS,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE;IAAK;IACZC,KAAK,EAAE,EAAE,CAAG;EACd,CAAC;EAED,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGd,mBAAmB,CAACjC,UAAU,CAAC;;IAExD;IACA,MAAMgD,cAAc,GAAG9C,aAAa,GAAG6C,gBAAgB;;IAEvD;IACA,MAAME,YAAY,GAAGb,yBAAyB,GAAGlC,aAAa;;IAE9D;IACA,MAAMgD,aAAa,GAAGN,KAAK,CAACO,UAAU,GAAGF,YAAY;IACrD,MAAMG,cAAc,GAAGR,KAAK,CAACS,WAAW,GAAGJ,YAAY;IACvD,MAAMK,eAAe,GAAGV,KAAK,CAACW,YAAY,GAAGN,YAAY;;IAEzD;IACA,MAAMO,iBAAiB,GAAIN,aAAa,GAAGb,iBAAiB,GAAI,GAAG;IACnE,MAAMoB,kBAAkB,GAAIL,cAAc,GAAGd,kBAAkB,GAAI,GAAG;IACtE,MAAMoB,mBAAmB,GAAIJ,eAAe,GAAGjB,iBAAiB,GAAI,GAAG;;IAEvE;IACA;IACA,MAAMsB,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACP,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvCQ,MAAM,EAAEF,IAAI,CAACC,GAAG,CAACN,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1CF,YAAY,EAAEG,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTK,KAAK,EAAEH,IAAI,CAACI,GAAG,CAACV,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGT,cAAc;MAAE;MACnFmB,SAAS,EAAEvB,KAAK,CAACO,UAAU;MAC3BiB,UAAU,EAAExB,KAAK,CAACS,WAAW;MAC7BgB,YAAY,EAAEzB,KAAK,CAACyB,YAAY;MAChCrB,cAAc,CAAC;IACjB,CAAC;EACH,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAACC,SAAS,EAAEjF,WAAW,KAAK;IACnD,MAAMkF,cAAc,GAAG7B,wBAAwB,CAAC4B,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACb,SAAS;IACxC,IAAIe,SAAS,GAAGF,cAAc,CAACZ,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAItE,WAAW,EAAE;MACfmF,SAAS,GAAGD,cAAc,CAACb,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,QAAQY,SAAS,CAACI,IAAI;MACpB,KAAK,YAAY;QACf;QACAD,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBb,SAAS,EAAEc,SAAS;MACpBb,SAAS,EAAEc;IACb,CAAC;EACH,CAAC;EACD;EACA,MAAME,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,IAAI;IAAE;IACnBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,GAAG;IAAE;IACpB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,QAAQ;IACdK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,IAAI;IAAE;IACrB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,CAAC;IAAE;IAClB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,0BAA0B;IAChC;IACAT,YAAY,EAAE,EAAE;IAAE;IAClBU,aAAa,EAAE,EAAE;IAAE;IACnB5B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBoB,IAAI,EAAE,SAAS;IACfK,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAsC,CAAC,EACrE;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAsC,CAAC,EACpE;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAsC,CAAC,EACrE;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAsC,CAAC,EACvE;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAsC,CAAC,EACrE;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAsC,CAAC,CACxE;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB3B,KAAK,EAAE;YAAE4B,KAAK,EAAE;UAAK,CAAC;UACtBzB,MAAM,EAAE;YAAEyB,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAI1G,QAAQ,CAAC2G,OAAO,EAAE;QACpB3G,QAAQ,CAAC2G,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,GAAG,CAAC;MAC7C;MACA,IAAI5G,gBAAgB,CAAC0G,OAAO,EAAE;QAC5B1G,gBAAgB,CAAC0G,OAAO,CAACK,GAAG,GAAG,iBAAiB;QAChD/G,gBAAgB,CAAC0G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACAH,OAAO,CAACI,GAAG,CAAC,kCAAkC,CAAC;MAC/C9G,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMyG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACnH,QAAQ,CAAC2G,OAAO,IAAI,CAACzG,SAAS,CAACyG,OAAO,EAAE,OAAO,IAAI;IAExD,MAAMS,MAAM,GAAGlH,SAAS,CAACyG,OAAO;IAChC,MAAMH,KAAK,GAAGxG,QAAQ,CAAC2G,OAAO;IAC9BS,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;IACjC,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;IACvD,OAAOmC,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IAC9D,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,YAAW;MACtB,MAAMb,MAAM,GAAG/H,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAM+H,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MACnCJ,MAAM,CAACtC,KAAK,GAAGgD,GAAG,CAACI,YAAY;MAC/Bd,MAAM,CAACnC,MAAM,GAAG6C,GAAG,CAACK,aAAa;MACjCZ,GAAG,CAACE,SAAS,CAACK,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAExB,IAAI;QACF,MAAMM,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEjB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrE,MAAMqD,IAAI,GAAGF,SAAS,CAACE,IAAI;QAC3B,MAAMxD,KAAK,GAAGsC,MAAM,CAACtC,KAAK;QAC1B,MAAMG,MAAM,GAAGmC,MAAM,CAACnC,MAAM;;QAE5B;QACA,MAAMsD,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,MAAM,GAAG,CAAC,EAAEwD,CAAC,EAAE,EAAE;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,KAAK,GAAG,CAAC,EAAE4D,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAG3D,KAAK,GAAG4D,CAAC,IAAI,CAAC;YAC/B,MAAME,CAAC,GAAGN,IAAI,CAACK,GAAG,CAAC;YACnB,MAAME,CAAC,GAAGP,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;YACvB,MAAMG,CAAC,GAAGR,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;;YAEvB;YACA,IAAII,MAAM,GAAG,KAAK;YAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;gBAC/B,IAAIA,EAAE,KAAK,CAAC,IAAID,EAAE,KAAK,CAAC,EAAE;gBAC1B,MAAME,WAAW,GAAG,CAAC,CAACT,CAAC,GAAGO,EAAE,IAAIlE,KAAK,IAAI4D,CAAC,GAAGO,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAME,EAAE,GAAGb,IAAI,CAACY,WAAW,CAAC;gBAC5B,MAAME,EAAE,GAAGd,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;gBAChC,MAAMG,EAAE,GAAGf,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;;gBAEhC;gBACA,MAAMI,SAAS,GAAGvE,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGO,EAAE,CAAC,GAAGpE,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGO,EAAE,CAAC,GAAGrE,IAAI,CAACwE,GAAG,CAACT,CAAC,GAAGO,EAAE,CAAC;gBACxE,IAAIC,SAAS,GAAG,EAAE,EAAE;kBAClBP,MAAM,GAAG,IAAI;kBACb;gBACF;cACF;cACA,IAAIA,MAAM,EAAE;YACd;YAEA,IAAIA,MAAM,EAAE;cACVR,UAAU,CAACiB,GAAG,CAACb,GAAG,GAAG,CAAC,CAAC;YACzB;UACF;QACF;;QAEA;QACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACoB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMb,CAAC,GAAGN,IAAI,CAACmB,CAAC,CAAC;UACjB,MAAMZ,CAAC,GAAGP,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMX,CAAC,GAAGR,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;;UAExB;UACA,MAAMG,UAAU,GAAG,CAAChB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;;UAElC;UACA,MAAMe,UAAU,GAAGtB,UAAU,CAACuB,GAAG,CAACH,UAAU,CAAC;;UAE7C;UACA,MAAMI,WAAW,GACfnB,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACP/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB9D,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB,CAACe,UACF;;UAED;UACA,IAAIE,WAAW,EAAE;YACfzB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIG,UAAU,GAAG,GAAG,IAAI,CAACC,UAAU,EAAE;YAC1C;YACAvB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG1E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsD,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAC7C;QACF;QAEAlC,GAAG,CAACyC,YAAY,CAAC5B,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCR,UAAU,CAACZ,GAAG,GAAGI,MAAM,CAACM,SAAS,CAAC,WAAW,CAAC;;QAE9C;QACAE,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;QAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;QACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;MAEhC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVtD,OAAO,CAACuD,IAAI,CAAC,0BAA0B,EAAED,CAAC,CAAC;QAC3C;QACAxC,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;QAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;QACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;MAChC;IACF,CAAC;IAEDrC,GAAG,CAACwC,OAAO,GAAG,YAAW;MACvBxD,OAAO,CAACuD,IAAI,CAAC,sBAAsB,CAAC;MACpC;MACAzC,UAAU,CAAC/E,KAAK,CAACoH,MAAM,GAAG,MAAM;MAChCrC,UAAU,CAAC/E,KAAK,CAACqH,YAAY,GAAG,QAAQ;MACxCtC,UAAU,CAAC/E,KAAK,CAACsH,OAAO,GAAG,GAAG;IAChC,CAAC;IAEDrC,GAAG,CAACd,GAAG,GAAGY,UAAU,CAACZ,GAAG;EAC1B,CAAC;;EAED;EACA,MAAMuD,qBAAqB,GAAInC,SAAS,IAAK;IAC3C;IACA,OAAOrD,IAAI,CAACyF,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACzK,QAAQ,CAAC2G,OAAO,IAAI,CAACzG,SAAS,CAACyG,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAGxG,QAAQ,CAAC2G,OAAO;IAC9B,MAAMS,MAAM,GAAGlH,SAAS,CAACyG,OAAO;IAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAJ,MAAM,CAACtC,KAAK,GAAG0B,KAAK,CAACa,UAAU;IAC/BD,MAAM,CAACnC,MAAM,GAAGuB,KAAK,CAACc,WAAW;;IAEjC;IACAC,GAAG,CAACE,SAAS,CAACjB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;;IAEvD;IACA,MAAMyF,cAAc,GAAGlE,KAAK,CAACmE,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAGtE,KAAK,CAACa,UAAU,GAAGb,KAAK,CAACc,WAAW;IACxD,MAAMyD,eAAe,GAAGH,aAAa,CAAC9F,KAAK,GAAG8F,aAAa,CAAC3F,MAAM;IAElE,IAAI+F,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAAC3F,MAAM;MACpC+F,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAAC9F,KAAK,IAAI,CAAC;MAClDqG,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAAC9F,KAAK;MAClCmG,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAAC3F,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMmG,MAAM,GAAGhE,MAAM,CAACtC,KAAK,GAAGkG,YAAY;IAC1C,MAAMK,MAAM,GAAGjE,MAAM,CAACnC,MAAM,GAAGgG,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAGvG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIgG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAGxG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAGzG,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAGwG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAG1G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAGsG,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAG3G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIgG,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAG5G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIiG,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAG7G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACtC,KAAK,GAAG4G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAG9G,IAAI,CAACI,GAAG,CAACiC,MAAM,CAACnC,MAAM,GAAG0G,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGvE,GAAG,CAACc,YAAY,CAACiD,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAACxD,IAAI;;MAEnC;MACA,MAAM0D,eAAe,GAAGzE,GAAG,CAACc,YAAY,CAACqD,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAC1D,IAAI;MAEvC,IAAI4D,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAMyD,UAAU,GAAG3D,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAI/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAM2D,UAAU,GAAG5D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAI/D,IAAI,CAACwE,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAI9D,IAAI,CAACwE,GAAG,CAACV,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAM2D,UAAU,GAAG7D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAM4D,UAAU,GAAG9D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAOyD,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,QAAQ,CAACrC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAMb,CAAC,GAAGmD,QAAQ,CAACtC,CAAC,CAAC;QACrB,MAAMZ,CAAC,GAAGkD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QACzB,MAAMX,CAAC,GAAGiD,QAAQ,CAACtC,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBoD,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,UAAU,CAACvC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAMb,CAAC,GAAGqD,UAAU,CAACxC,CAAC,CAAC;QACvB,MAAMZ,CAAC,GAAGoD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAMX,CAAC,GAAGmD,UAAU,CAACxC,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAI6C,UAAU,CAAC1D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBsD,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACdD,OAAO,CAACuD,IAAI,CAAC,uBAAuB,EAAEtD,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMgG,2BAA2B,GAAGA,CAACC,WAAW,EAAEnF,WAAW,KAAK;IAChE;IACAvH,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMkF,SAAS,GAAGK,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnH,IAAI,KAAKiH,WAAW,CAAC;;IAE3D;IACAhK,UAAU,CAAC,MAAM;MACf1C,kBAAkB,CAAC;QACjByF,IAAI,EAAEiH,WAAW;QACjB/G,QAAQ,EAAE,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,QAAQ,KAAI,EAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAMkH,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAChN,UAAU,EAAE;MACf,MAAMiN,eAAe,GAAGjG,YAAY,CAAC,CAAC;MACtC,IAAIlH,gBAAgB,CAAC0G,OAAO,IAAIyG,eAAe,EAAE;QAC/CnN,gBAAgB,CAAC0G,OAAO,CAACK,GAAG,GAAGoG,eAAe;QAC9CnN,gBAAgB,CAAC0G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,OAAO;MAClD;MACA7G,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC/BI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACA,IAAIZ,SAAS,CAACyG,OAAO,IAAI3G,QAAQ,CAAC2G,OAAO,EAAE;QACzC,MAAMS,MAAM,GAAGlH,SAAS,CAACyG,OAAO;QAChC,MAAMY,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QACnCJ,MAAM,CAACtC,KAAK,GAAG9E,QAAQ,CAAC2G,OAAO,CAACU,UAAU;QAC1CD,MAAM,CAACnC,MAAM,GAAGjF,QAAQ,CAAC2G,OAAO,CAACW,WAAW;QAC5CC,GAAG,CAACE,SAAS,CAACzH,QAAQ,CAAC2G,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAMyB,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEjB,MAAM,CAACtC,KAAK,EAAEsC,MAAM,CAACnC,MAAM,CAAC;QACrEzE,cAAc,CAAC+J,qBAAqB,CAACnC,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACL1H,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM2M,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpN,gBAAgB,CAAC0G,OAAO,EAAE;MAC5B1G,gBAAgB,CAAC0G,OAAO,CAAC9D,KAAK,CAACoE,OAAO,GAAG,MAAM;IACjD;IACA7G,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BY,qBAAqB,CAAC,KAAK,CAAC;IAC5BR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMwM,kBAAkB,GAAIC,MAAM,IAAK;IACrCrM,aAAa,CAACqM,MAAM,CAAC;IACrBnM,gBAAgB,CAAC8B,mBAAmB,CAACqK,MAAM,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,IAAI,IAAK;IACtCrM,gBAAgB,CAACqM,IAAI,CAAC;EACxB,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnC/M,YAAY,CAAC+M,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvCd,2BAA2B,CAACc,OAAO,CAAC9H,IAAI,EAAEpF,SAAS,CAAC;EACtD,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACdqH,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArH,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,oBAAoB,IAAIpB,UAAU,EAAE;IAEzC,MAAM2N,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,MAAMjB,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7C7I,mBAAmB,CAACkL,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAACjL,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACgL,cAAc,IAAIjL,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMsM,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACvM,oBAAoB,EAAEpB,UAAU,EAAE0B,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACA3C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,iBAAiB,IAAI1B,UAAU,EAAE;MACpCuB,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMuM,iBAAiB,GAAGF,WAAW,CAAC,MAAM;MAC1CrM,YAAY,CAACwM,IAAI,IAAI;QACnB;QACA,IAAI,CAACvM,gBAAgB,EAAE;UACrBqM,aAAa,CAACC,iBAAiB,CAAC;UAChCnM,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAIoM,IAAI,IAAI,CAAC,EAAE;UACb;UACAF,aAAa,CAACC,iBAAiB,CAAC;UAChCnM,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChC2L,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOe,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMF,aAAa,CAACC,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACpM,iBAAiB,EAAE1B,UAAU,EAAEwB,gBAAgB,CAAC,CAAC;;EAErD;EACA,MAAMwM,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAAC7M,oBAAoB;IACtCC,uBAAuB,CAAC4M,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACbtM,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMyM,mBAAmB,GAAGA,CAAA,KAAM;IAChC7M,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BJ,aAAa,CAAC,KAAK,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpBiM,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO3N,SAAS,KAAK,SAAS,GAAGkF,OAAO,GAAGK,SAAS;EACtD,CAAC;;EAED;EACA,MAAMqI,gBAAgB,GAAInE,CAAC,IAAK;IAC9BlI,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAACgI,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC;EACjC,CAAC;EAED,MAAMC,eAAe,GAAItE,CAAC,IAAK;IAC7B,IAAI,CAACnI,UAAU,EAAE;IAEjB,MAAM0M,QAAQ,GAAGvE,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACrC,MAAMG,IAAI,GAAGD,QAAQ,GAAGxM,MAAM;;IAE9B;IACA,IAAIyM,IAAI,GAAG,CAAC,EAAE;MACZ5M,gBAAgB,CAAC4M,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC5M,UAAU,EAAE;IAEjBC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvBrB,uBAAuB,CAAC,KAAK,CAAC;IAChC;;IAEA;IACAsB,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd,MAAMgQ,kBAAkB,GAAI1E,CAAC,IAAK;MAChC,IAAI/I,kBAAkB,IAAI,CAAC+I,CAAC,CAAC2E,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7D1N,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDjC,QAAQ,CAAC0D,gBAAgB,CAAC,WAAW,EAAE+L,kBAAkB,CAAC;IAC1DzP,QAAQ,CAAC0D,gBAAgB,CAAC,YAAY,EAAE+L,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACXzP,QAAQ,CAAC4D,mBAAmB,CAAC,WAAW,EAAE6L,kBAAkB,CAAC;MAC7DzP,QAAQ,CAAC4D,mBAAmB,CAAC,YAAY,EAAE6L,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACzN,kBAAkB,CAAC,CAAC;;EAExB;EACA,oBACEpC,OAAA;IAAK4D,KAAK,EAAEoM,MAAM,CAACC,SAAU;IAAAC,QAAA,gBAC3BlQ,OAAA;MAAK4D,KAAK,EAAEoM,MAAM,CAACG,eAAgB;MAAAD,QAAA,gBACjClQ,OAAA;QACEoQ,GAAG,EAAErP,QAAS;QACd6C,KAAK,EAAEoM,MAAM,CAACK,UAAW;QACzBC,QAAQ;QACRC,WAAW;QACXC,KAAK;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACF5Q,OAAA;QAAQoQ,GAAG,EAAEnP,SAAU;QAAC2C,KAAK,EAAE;UAAEoE,OAAO,EAAE;QAAO;MAAE;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD5Q,OAAA;QACEoQ,GAAG,EAAEpP,gBAAiB;QACtB4C,KAAK,EAAEoM,MAAM,CAACa,aAAc;QAC5BC,GAAG,EAAC;MAAe;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAAC1P,UAAU,iBACVlB,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAACe,OAAQ;QACtBC,OAAO,EAAEnQ,YAAa;QACtB,cAAW,MAAM;QAAAqP,QAAA,EAClB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGA,CAAC1P,UAAU,iBACVlB,OAAA;QAAK4D,KAAK,EAAE;UAAEqN,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAEpP,QAAQ,GAAG,EAAE,GAAG,EAAE;UAAEqP,KAAK,EAAErP,QAAQ,GAAG,EAAE,GAAG,EAAE;UAAEsP,MAAM,EAAE,EAAE;UAAEpJ,OAAO,EAAE,MAAM;UAAEqJ,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnKlQ,OAAA;UAAO4D,KAAK,EAAE;YAAE2N,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE3P,QAAQ,GAAG,EAAE,GAAG,EAAE;YAAE4P,YAAY,EAAE,CAAC;YAAEC,aAAa,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAAY;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5I5Q,OAAA;UAAO4R,SAAS,EAAC,QAAQ;UAAA1B,QAAA,gBACvBlQ,OAAA;YACE2G,IAAI,EAAC,UAAU;YACfkL,OAAO,EAAEvP,oBAAqB;YAC9BwP,QAAQ,EAAE5C,uBAAwB;YAClC6C,QAAQ,EAAEnP,iBAAkB;YAC5B,cAAW;UAAqB;YAAA6N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF5Q,OAAA;YAAM4R,SAAS,EAAC,QAAQ;YAAA1B,QAAA,eACtBlQ,OAAA;cAAM4R,SAAS,EAAC,QAAQ;cAAA1B,QAAA,gBACtBlQ,OAAA;gBAAK4R,SAAS,EAAC,WAAW;gBAACI,OAAO,EAAC,WAAW;gBAAA9B,QAAA,eAAClQ,OAAA;kBAAUiS,MAAM,EAAC,kBAAkB;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpL5Q,OAAA;gBAAK4R,SAAS,EAAC,OAAO;gBAACI,OAAO,EAAC,WAAW;gBAAA9B,QAAA,gBAAClQ,OAAA;kBAAMuS,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAAA5Q,OAAA;kBAAMuS,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACP,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGA1P,UAAU,iBACTlB,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAAC2C,OAAQ;QACtB3B,OAAO,EAAE5B,mBAAoB;QAC7B,cAAW,MAAM;QAAAc,QAAA,EAClB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGAhO,iBAAiB,iBAChB5C,OAAA;QAAK4D,KAAK,EAAEoM,MAAM,CAAC4C,gBAAiB;QAAA1C,QAAA,gBAClClQ,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAAC6C,eAAgB;UAAA3C,QAAA,EAAE1N;QAAS;UAAAiO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD5Q,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAAC8C,aAAc;UAAA5C,QAAA,EAAC;QAAiB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGAtO,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9D5C,OAAA;QAAK4D,KAAK,EAAEoM,MAAM,CAAC+C,aAAc;QAAA7C,QAAA,gBAC/BlQ,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAACgD,UAAW;UAAA9C,QAAA,EAAC;QAA6C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClF5Q,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAACiD,aAAc;UAAA/C,QAAA,EAAC;QAAgD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CACN,EAEAtO,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7D5C,OAAA;QAAK4D,KAAK,EAAEoM,MAAM,CAAC+C,aAAc;QAAA7C,QAAA,eAC/BlQ,OAAA;UAAK4D,KAAK,EAAE;YAAC,GAAGoM,MAAM,CAACgD,UAAU;YAAEE,eAAe,EAAE;UAAyB,CAAE;UAAAhD,QAAA,EAAC;QAEhF;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhP,aAAa,iBACZ5B,OAAA;QACE4D,KAAK,EAAE;UACL,GAAGoM,MAAM,CAACmD,SAAS;UACnBjI,OAAO,EAAE5I,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7DsI,MAAM,EAAE1I,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACFkP,SAAS,EAAE9P,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAAoO,QAAA,eAElBlQ,OAAA;UAAKgS,OAAO,EAAC,aAAa;UAACoB,KAAK,EAAC,4BAA4B;UAAAlD,QAAA,gBAE3DlQ,OAAA;YACEqT,CAAC,EAAC,4EAA4E;YAC9ElB,MAAM,EACJ7P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0P,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF5Q,OAAA;YACEqT,CAAC,EAAC,4EAA4E;YAC9ElB,MAAM,EACJ7P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0P,WAAW,EAAC,GAAG;YACfF,IAAI,EAAC,MAAM;YACXG,aAAa,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEF5Q,OAAA;YACEyJ,CAAC,EAAC,KAAK;YACPD,CAAC,EAAC,KAAK;YACP3D,KAAK,EAAC,KAAK;YACXG,MAAM,EAAC,KAAK;YACZkM,IAAI,EACF5P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDwI,OAAO,EAAE5I,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpE4Q,EAAE,EAAC,IAAI;YACPnB,MAAM,EACJ7P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0P,WAAW,EAAC;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEF5Q,OAAA;YACEuT,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACR7J,CAAC,EAAC,IAAI;YACNuI,IAAI,EACF5P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDwI,OAAO,EAAE5I,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClEyP,MAAM,EACJ7P,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD0P,WAAW,EAAC;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAEDtO,oBAAoB,iBACnBtC,OAAA,CAAAE,SAAA;YAAAgQ,QAAA,gBACElQ,OAAA;cAAMyJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACiK,UAAU,EAAC,QAAQ;cAACvB,IAAI,EAAC,OAAO;cAACT,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAEvF;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP5Q,OAAA;cAAMyJ,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACiK,UAAU,EAAC,QAAQ;cAACvB,IAAI,EAAC,OAAO;cAACT,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAEvF;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5Q,OAAA;QAAK4D,KAAK,EAAE;UAAEqN,QAAQ,EAAE,UAAU;UAAEpL,KAAK,EAAE,CAAC;UAAEG,MAAM,EAAE;QAAE,CAAE;QAAAkK,QAAA,eACxDlQ,OAAA;UAAAkQ,QAAA,eACElQ,OAAA;YAAUQ,EAAE,EAAC,iBAAiB;YAAA0P,QAAA,eAI5BlQ,OAAA;cAAQuT,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAAC7J,CAAC,EAAC;YAAM;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGLxP,eAAe,iBACdpB,OAAA;QAAK4D,KAAK,EAAE;UACV,GAAGoM,MAAM,CAAC0D,eAAe;UACzB7N,KAAK,EAAEnE,SAAS,KAAK,SAAS,GAAG,GAAG6C,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;UACzEwB,MAAM,EAAEtE,SAAS,KAAK,SAAS,GAAG,GAAG+C,YAAY,GAAG,GAAG,GAAGC,eAAe,GAAG;UAC5E;UACAiP,QAAQ,EAAEjS,SAAS,KAAK,SAAS,IAAIQ,aAAa,GAAG+B,mBAAmB,CAACjC,UAAU,CAAC,GAAG,GAAG,GACtF,uBAAuB,GACvB;QACN,CAAE;QAAAkO,QAAA,eACAlQ,OAAA;UAAK4D,KAAK,EAAE;YACVqN,QAAQ,EAAE,UAAU;YACpBpL,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdgC,OAAO,EAAE,MAAM;YACfsJ,UAAU,EAAE,QAAQ;YACpBsC,cAAc,EAAE;UAClB,CAAE;UAAA1D,QAAA,gBACAlQ,OAAA;YACE+H,GAAG,EAAE,OAAO3G,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAAC0F,IAAI,GAAG1F,eAAgB;YAClF0P,GAAG,EAAC,kBAAkB;YACtBlN,KAAK,EAAE;cACLiC,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACd6N,SAAS,EAAE,SAAS;cACpBC,SAAS,EAAEpS,SAAS,KAAK,WAAW,GAChC,uBAAuBgD,eAAe,GAAG,EAAE,IAAIpD,WAAW,GAAG,aAAa,GAAG,EAAE,EAAE,GACjF,SAAUmD,YAAY,GAAG,EAAE,IAAKvC,aAAa,GAAG+B,mBAAmB,CAACjC,UAAU,CAAC,CAAC,GAAG;cACvFgJ,MAAM,EAAE;YACV,CAAE;YACF+I,MAAM,EAAG5I,CAAC,IAAKzC,gBAAgB,CAACyC,CAAC,CAAC2E,MAAM,EAAEpO,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU;UAAE;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,EACDlP,SAAS,KAAK,SAAS,IAAI,OAAON,eAAe,KAAK,QAAQ,iBAC7DpB,OAAA;YAAK4D,KAAK,EAAE;cACVqN,QAAQ,EAAE,UAAU;cACpB+C,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXH,SAAS,EAAE,kBAAkB;cAC7BrC,QAAQ,EAAE,MAAM;cAChBD,UAAU,EAAE,KAAK;cACjBD,KAAK,EAAE,OAAO;cACd2B,eAAe,EAAE,yBAAyB;cAC1CgB,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,2BAA2B;cACtClD,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,GACC9O,eAAe,CAAC4F,QAAQ,EAAC,IAC1B,EAAC9E,aAAa,KAAK+B,mBAAmB,CAACjC,UAAU,CAAC,iBAChDhC,OAAA;cAAM4D,KAAK,EAAE;gBACX6N,QAAQ,EAAE,MAAM;gBAChBvG,OAAO,EAAE,GAAG;gBACZqJ,UAAU,EAAE;cACd,CAAE;cAAArE,QAAA,GAAC,UACO,EAAC,CAAC,CAAChO,aAAa,GAAG+B,mBAAmB,CAACjC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwS,OAAO,CAAC,CAAC,CAAC,EAAC,IACpF;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5Q,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAACyE,UAAW;QACzB7C,SAAS,EAAE9P,QAAQ,GAAG,oBAAoB,GAAG,EAAG;QAChDkP,OAAO,EAAE9C,aAAc;QACvB,cAAYhN,UAAU,GAAG,iBAAiB,GAAG,SAAU;QAAAgP,QAAA,EAEtD,CAAChP,UAAU,gBACVlB,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAAC0E,YAAa;UAAC9C,SAAS,EAAE9P,QAAQ,GAAG,qBAAqB,GAAG;QAAG;UAAA2O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAEzF5Q,OAAA;UAAK6F,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACgM,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAAhC,QAAA,eAC1DlQ,OAAA;YAAMqT,CAAC,EAAC;UAA6D;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGR1P,UAAU,iBACTlB,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAAC2E,QAAS;QACvB3D,OAAO,EAAEA,CAAA,KAAM1N,MAAM,CAACsR,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC,cAAW,OAAO;QAAA3E,QAAA,eAElBlQ,OAAA;UAAK6F,KAAK,EAAC,IAAI;UAACG,MAAM,EAAC,IAAI;UAACgM,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,OAAO;UAAAhC,QAAA,eAC1DlQ,OAAA;YAAMqT,CAAC,EAAC;UAAyN;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL1P,UAAU,iBACTlB,OAAA;MACE4D,KAAK,EAAEoM,MAAM,CAAC8E,oBAAqB;MACnClD,SAAS,EAAE9P,QAAQ,GAAG,YAAY,GAAG,EAAG;MACxCkP,OAAO,EAAEA,CAAA,KAAM3O,qBAAqB,CAAC,IAAI,CAAE;MAC3C,cAAW,mBAAmB;MAAA6N,QAAA,gBAE9BlQ,OAAA;QAAK6F,KAAK,EAAC,IAAI;QAACG,MAAM,EAAC,IAAI;QAACgM,OAAO,EAAC,WAAW;QAACE,IAAI,EAAC,OAAO;QAAAhC,QAAA,eAC1DlQ,OAAA;UAAMqT,CAAC,EAAC;QAA64B;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACp5B,CAAC,eACN5Q,OAAA;QAAM4D,KAAK,EAAEoM,MAAM,CAAC+E,aAAc;QAAA7E,QAAA,GAAEhO,aAAa,EAAC,IAAE;MAAA;QAAAuO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACT,EAGAxO,kBAAkB,iBACjBpC,OAAA;MACE4D,KAAK,EAAEoM,MAAM,CAACgF,YAAa;MAC3BhE,OAAO,EAAEA,CAAA,KAAM3O,qBAAqB,CAAC,KAAK,CAAE;MAC5CuP,SAAS,EAAC,eAAe;MAAA1B,QAAA,eAEzBlQ,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAACiF,cAAe;QAC7BjE,OAAO,EAAG7F,CAAC,IAAKA,CAAC,CAAC+J,eAAe,CAAC,CAAE;QACpCtD,SAAS,EAAC,eAAe;QAAA1B,QAAA,gBAEzBlQ,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAACmF,WAAY;UAAAjF,QAAA,gBAC7BlQ,OAAA;YAAI4D,KAAK,EAAEoM,MAAM,CAACoF,UAAW;YAAAlF,QAAA,EAAC;UAAiB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpD5Q,OAAA;YACE4D,KAAK,EAAEoM,MAAM,CAACqF,aAAc;YAC5BrE,OAAO,EAAEA,CAAA,KAAM3O,qBAAqB,CAAC,KAAK,CAAE;YAC5C,cAAW,OAAO;YAAA6N,QAAA,eAElBlQ,OAAA;cAAK6F,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACgM,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,cAAc;cAAAhC,QAAA,eACjElQ,OAAA;gBAAMqT,CAAC,EAAC;cAAuG;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5Q,OAAA;UAAK4D,KAAK,EAAEoM,MAAM,CAACsF,YAAa;UAAApF,QAAA,gBAE9BlQ,OAAA;YAAK4D,KAAK,EAAEoM,MAAM,CAACuF,eAAgB;YAAArF,QAAA,gBACjClQ,OAAA;cACE4D,KAAK,EAAE;gBACL,GAAGoM,MAAM,CAACwF,YAAY;gBACtB,IAAIxT,UAAU,KAAK,KAAK,GAAGgO,MAAM,CAACyF,kBAAkB,GAAG,CAAC,CAAC;cAC3D,CAAE;cACFzE,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,KAAK,CAAE;cAAA6B,QAAA,EAC1C;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5Q,OAAA;cACE4D,KAAK,EAAE;gBACL,GAAGoM,MAAM,CAACwF,YAAY;gBACtB,IAAIxT,UAAU,KAAK,OAAO,GAAGgO,MAAM,CAACyF,kBAAkB,GAAG,CAAC,CAAC;cAC7D,CAAE;cACFzE,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,OAAO,CAAE;cAAA6B,QAAA,EAC5C;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5Q,OAAA;YAAK4D,KAAK,EAAEoM,MAAM,CAAC0F,eAAgB;YAAAxF,QAAA,gBACjClQ,OAAA;cAAO4D,KAAK,EAAEoM,MAAM,CAAC2F,WAAY;cAAAzF,QAAA,GAAC,cACpB,EAAChO,aAAa,EAAC,IAC3B,EAACA,aAAa,KAAK+B,mBAAmB,CAACjC,UAAU,CAAC,iBAChDhC,OAAA;gBAAM4D,KAAK,EAAEoM,MAAM,CAAC4F,UAAW;gBAAA1F,QAAA,GAAC,GAC7B,EAAChO,aAAa,GAAG+B,mBAAmB,CAACjC,UAAU,CAAC,GAAG,GAAG,GAAG,EAAE,EAC3D,CAAC,CAACE,aAAa,GAAG+B,mBAAmB,CAACjC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,EAAEwS,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5E;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5Q,OAAA;cACE2G,IAAI,EAAC,OAAO;cACZT,GAAG,EAAC,IAAI;cACRH,GAAG,EAAC,IAAI;cACR8P,KAAK,EAAE3T,aAAc;cACrB4P,QAAQ,EAAG3G,CAAC,IAAKoD,qBAAqB,CAACuH,QAAQ,CAAC3K,CAAC,CAAC2E,MAAM,CAAC+F,KAAK,CAAC,CAAE;cACjEjS,KAAK,EAAEoM,MAAM,CAAC+F;YAAO;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACF5Q,OAAA;cAAK4D,KAAK,EAAEoM,MAAM,CAACgG,YAAa;cAAA9F,QAAA,gBAC9BlQ,OAAA;gBAAAkQ,QAAA,EAAM;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB5Q,OAAA;gBAAAkQ,QAAA,EAAM;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGN5Q,OAAA;cAAK4D,KAAK,EAAEoM,MAAM,CAACiG,aAAc;cAAA/F,QAAA,gBAC/BlQ,OAAA;gBACE4D,KAAK,EAAEoM,MAAM,CAACkG,YAAa;gBAC3BlF,OAAO,EAAEA,CAAA,KAAMzC,qBAAqB,CAAC,EAAE,CAAE;gBAAA2B,QAAA,EAC1C;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5Q,OAAA;gBACE4D,KAAK,EAAEoM,MAAM,CAACkG,YAAa;gBAC3BlF,OAAO,EAAEA,CAAA,KAAMzC,qBAAqB,CAACtK,mBAAmB,CAACjC,UAAU,CAAC,CAAE;gBAAAkO,QAAA,GACvE,WACU,EAACjM,mBAAmB,CAACjC,UAAU,CAAC,EAAC,KAC5C;cAAA;gBAAAyO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5Q,OAAA;gBACE4D,KAAK,EAAEoM,MAAM,CAACkG,YAAa;gBAC3BlF,OAAO,EAAEA,CAAA,KAAMzC,qBAAqB,CAAC,EAAE,CAAE;gBAAA2B,QAAA,EAC1C;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApP,oBAAoB,iBACnBxB,OAAA;MACEoQ,GAAG,EAAEhN,QAAS;MACdQ,KAAK,EAAE;QACL,GAAGoM,MAAM,CAACmG,gBAAgB;QAC1BrC,SAAS,EAAE,cAAchR,aAAa,KAAK;QAC3CsT,WAAW,EAAE;MACf,CAAE;MACFxE,SAAS,EAAE9P,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjBuU,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAEhH,gBAAiB;MAC/BiH,WAAW,EAAE9G,eAAgB;MAC7B+G,UAAU,EAAE5G,cAAe;MAAAM,QAAA,gBAE3BlQ,OAAA;QACE4D,KAAK,EAAEoM,MAAM,CAACyG,UAAW;QACzB,eAAY,MAAM;QAClBH,YAAY,EAAEhH,gBAAiB;QAC/BiH,WAAW,EAAE9G,eAAgB;QAC7B+G,UAAU,EAAE5G;MAAe;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACF5Q,OAAA;QAAK4D,KAAK,EAAEoM,MAAM,CAAC0G,WAAY;QAAAxG,QAAA,gBAC7BlQ,OAAA;UACE4D,KAAK,EAAE;YACL,GAAGoM,MAAM,CAAC2G,GAAG;YACb,IAAIjV,SAAS,KAAK,SAAS,GAAGsO,MAAM,CAACtO,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACFsP,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAAC,SAAS,CAAE;UAAAyB,QAAA,EAC3C;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5Q,OAAA;UACE4D,KAAK,EAAE;YACL,GAAGoM,MAAM,CAAC2G,GAAG;YACb,IAAIjV,SAAS,KAAK,WAAW,GAAGsO,MAAM,CAACtO,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACFsP,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAAC,WAAW,CAAE;UAAAyB,QAAA,EAC7C;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5Q,OAAA;QAAK4D,KAAK,EAAEoM,MAAM,CAAC4G,aAAc;QAAChF,SAAS,EAAC,gBAAgB;QAAA1B,QAAA,EACzDb,kBAAkB,CAAC,CAAC,CAACwH,GAAG,CAAC,CAACjI,OAAO,EAAEkI,KAAK,KAAK;UAC5C;UACA,IAAI,CAAClI,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMmI,UAAU,GAAG,CAAC,OAAO3V,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0F,IAAI,GAAG1F,eAAe,MAAMwN,OAAO,CAAC9H,IAAI;UAEnH,oBACE9G,OAAA;YAEE4D,KAAK,EAAE;cACL,GAAGoM,MAAM,CAACgH,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/C7D,eAAe,EAAE6D,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFG,KAAK,EAAE,GAAGtI,OAAO,CAAC/H,IAAI,MAAM+H,OAAO,CAACvI,YAAY,IAAI,KAAK,IAAK;YAC9D2K,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAC/H,IAAI,IAAI+H,OAAO,CAACvI,YAAY,IAAI,KAAK,IAAK;YAAA6J,QAAA,gBAExElQ,OAAA;cACE+H,GAAG,EAAE6G,OAAO,CAAC9H,IAAK;cAClBgK,GAAG,EAAElC,OAAO,CAAC/H,IAAK;cAClBjD,KAAK,EAAEoM,MAAM,CAACmH,YAAa;cAC3BC,OAAO,EAAGjM,CAAC,IAAK;gBACdA,CAAC,CAAC2E,MAAM,CAACpE,aAAa,CAAC9H,KAAK,CAACoE,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5Q,OAAA;cAAK4D,KAAK,EAAEoM,MAAM,CAACqH,YAAa;cAAAnH,QAAA,gBAC9BlQ,OAAA;gBAAK4D,KAAK,EAAEoM,MAAM,CAACsH,WAAY;gBAAApH,QAAA,EAAEtB,OAAO,CAAC/H;cAAI;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnDlP,SAAS,KAAK,SAAS,IAAIkN,OAAO,CAACvI,YAAY,iBAC9CrG,OAAA;gBAAK4D,KAAK,EAAEoM,MAAM,CAACuH,WAAY;gBAAArH,QAAA,GAAEtB,OAAO,CAACvI,YAAY,EAAC,IAAE;cAAA;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBDkG,KAAK;YAAArG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA9P,EAAA,CArvCMF,KAAK;AAAA4W,EAAA,GAAL5W,KAAK;AAsvCX,MAAMoP,MAAM,GAAG;EACbC,SAAS,EAAE;IACTgB,QAAQ,EAAE,UAAU;IACpBjL,MAAM,EAAE,4BAA4B;IACpCgC,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvB6B,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,MAAM;IACbkG,UAAU,EAAE,4EAA4E;IACxFC,QAAQ,EAAE,QAAQ;IAClBtB,WAAW,EAAE,cAAc;IAC3BuB,uBAAuB,EAAE,aAAa;IACtCC,uBAAuB,EAAE,OAAO,CAAC;EACnC,CAAC;EACDzH,eAAe,EAAE;IACf0H,IAAI,EAAE,CAAC;IACP5G,QAAQ,EAAE,UAAU;IACpByG,QAAQ,EAAE,QAAQ;IAClBxE,eAAe,EAAE,MAAM;IACvBlL,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE;EAClB,CAAC;EACDvD,UAAU,EAAE;IACVxK,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd6N,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACDjD,aAAa,EAAE;IACbI,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACN+C,IAAI,EAAE,CAAC;IACPpO,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd6N,SAAS,EAAE,OAAO;IAClB7L,OAAO,EAAE,MAAM;IACf8P,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAED/G,OAAO,EAAE;IACPE,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX+C,IAAI,EAAE,MAAM;IACZf,eAAe,EAAE,oBAAoB;IACrC3B,KAAK,EAAE,OAAO;IACd2C,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjB3G,MAAM,EAAE,EAAE;IACV4G,MAAM,EAAE,MAAM;IACd1D,SAAS,EAAE,+BAA+B;IAC1C2D,UAAU,EAAE,eAAe;IAC3BjQ,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxB/N,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkS,OAAO,EAAE;EACX,CAAC;EACDvF,OAAO,EAAE;IACP1B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX+C,IAAI,EAAE,MAAM;IACZf,eAAe,EAAE,oBAAoB;IACrC3B,KAAK,EAAE,OAAO;IACd2C,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjB3G,MAAM,EAAE,EAAE;IACV4G,MAAM,EAAE,MAAM;IACd1D,SAAS,EAAE,+BAA+B;IAC1C2D,UAAU,EAAE,eAAe;IAC3BjQ,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxB/N,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkS,OAAO,EAAE;EACX,CAAC;EACDC,eAAe,EAAE;IACflH,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,EAAE;IACVpJ,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpB8G,GAAG,EAAE,KAAK;IACVlE,OAAO,EAAE,MAAM;IACfhB,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpBkE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClChE,SAAS,EAAE,+BAA+B;IAC1C0D,MAAM,EAAE;EACV,CAAC;EACDO,WAAW,EAAE;IACXtH,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACX+C,IAAI,EAAE,MAAM;IACZpO,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkN,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpB6D,MAAM,EAAE,oCAAoC;IAC5C5G,MAAM,EAAE,CAAC;IACT6G,UAAU,EAAE;EACd,CAAC;EACDO,YAAY,EAAE;IACZvH,QAAQ,EAAE,UAAU;IACpBpL,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdmO,YAAY,EAAE,KAAK;IACnB6D,MAAM,EAAE,MAAM;IACdD,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BjQ,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBxC,MAAM,EAAE,EAAE;IACVqH,MAAM,EAAE,KAAK;IACbnE,SAAS,EAAE,8BAA8B;IACzC4D,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACTpE,SAAS,EAAE;IACb;EACF,CAAC;EACD4E,WAAW,EAAE;IACXjH,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdoH,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChB1E,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBjB,eAAe,EAAE,oBAAoB;IACrCvB,aAAa,EAAE;EACjB,CAAC;EACDiB,gBAAgB,EAAE;IAChB3B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV+C,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClC1C,MAAM,EAAE,EAAE;IACVyH,SAAS,EAAE,QAAQ;IACnBxE,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBhB,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,MAAM;IACpBkE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClChE,SAAS,EAAE;EACb,CAAC;EACDzB,eAAe,EAAE;IACfpB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBoH,UAAU,EAAE,8BAA8B;IAC1CjH,YAAY,EAAE,KAAK;IACnBoH,SAAS,EAAE;EACb,CAAC;EACDhG,aAAa,EAAE;IACbrB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdoH,UAAU,EAAE;EACd,CAAC;EACD5F,aAAa,EAAE;IACb9B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV+C,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClC1C,MAAM,EAAE,EAAE;IACVyH,SAAS,EAAE,QAAQ;IACnBxE,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBjB,eAAe,EAAE,oBAAoB;IACrCmF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClChE,SAAS,EAAE;EACb,CAAC;EACDtB,UAAU,EAAE;IACVvB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdoH,UAAU,EAAE,8BAA8B;IAC1CzF,eAAe,EAAE,0BAA0B;IAC3CgB,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBzC,YAAY,EAAE,KAAK;IACnBuG,UAAU,EAAE;EACd,CAAC;EACDhF,aAAa,EAAE;IACbxB,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdoH,UAAU,EAAE,8BAA8B;IAC1CzF,eAAe,EAAE,oBAAoB;IACrCgB,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDhB,SAAS,EAAE;IACTlC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV+C,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClCjO,KAAK,EAAE,KAAK;IACZkT,QAAQ,EAAE,OAAO;IACjB/S,MAAM,EAAE,MAAM;IACdkF,OAAO,EAAE,GAAG;IACZmJ,aAAa,EAAE,MAAM;IACrBjD,MAAM,EAAE,CAAC;IACTpG,MAAM,EAAE,iDAAiD;IACzDgO,YAAY,EAAE,iDAAiD;IAC/Df,UAAU,EAAE;EACd,CAAC;EACDvE,eAAe,EAAE;IACfzC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACV+C,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,uBAAuB;IAClC1C,MAAM,EAAE,CAAC;IACTpJ,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBS,aAAa,EAAE,MAAM;IACrB4E,QAAQ,EAAE,OAAO;IAAE;IACnBC,SAAS,EAAE,OAAO,CAAC;EACrB,CAAC;EACDzE,UAAU,EAAE;IACVxD,QAAQ,EAAE,UAAU;IACpB+C,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXH,SAAS,EAAE,kBAAkB;IAC7BjO,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkN,eAAe,EAAE,0BAA0B;IAC3CiB,YAAY,EAAE,KAAK;IACnBnM,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBmE,MAAM,EAAE,SAAS;IACjB3G,MAAM,EAAE,EAAE;IACV6G,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,oCAAoC;IAC5C1D,SAAS,EAAE,+BAA+B;IAC1C4D,OAAO,EAAE,MAAM;IACfhE,OAAO,EAAE,CAAC;IACVyD,uBAAuB,EAAE,aAAa;IACtCvB,WAAW,EAAE;EACf,CAAC;EACD1B,YAAY,EAAE;IACZ7O,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkN,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,KAAK;IACnB8D,UAAU,EAAE;EACd,CAAC;EACDtD,QAAQ,EAAE;IACR1D,QAAQ,EAAE,UAAU;IACpB+C,MAAM,EAAE,MAAM;IACd7C,KAAK,EAAE,MAAM;IACbtL,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdkN,eAAe,EAAE,oBAAoB;IACrCiB,YAAY,EAAE,KAAK;IACnBnM,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBmE,MAAM,EAAE,SAAS;IACjB3G,MAAM,EAAE,EAAE;IACV6G,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACd1D,SAAS,EAAE,+BAA+B;IAC1C4D,OAAO,EAAE,MAAM;IACfhE,OAAO,EAAE;EACX,CAAC;EAED;EACAY,oBAAoB,EAAE;IACpB7D,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACb+B,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,OAAO;IACd2C,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,MAAM;IACdhQ,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpB8G,GAAG,EAAE,KAAK;IACVF,OAAO,EAAE,MAAM;IACfD,UAAU,EAAE,eAAe;IAC3B7G,MAAM,EAAE,EAAE;IACVkD,SAAS,EAAE,mCAAmC;IAC9C4E,SAAS,EAAE,MAAM;IACjBD,QAAQ,EAAE,MAAM;IAChBtB,uBAAuB,EAAE,aAAa;IACtCvB,WAAW,EAAE;EACf,CAAC;EACDrB,aAAa,EAAE;IACbtD,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE;EACd,CAAC;EAED;EACAwD,YAAY,EAAE;IACZ/D,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACN+C,IAAI,EAAE,CAAC;IACP9C,KAAK,EAAE,CAAC;IACR6C,MAAM,EAAE,CAAC;IACTd,eAAe,EAAE,oBAAoB;IACrClL,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBxC,MAAM,EAAE,EAAE;IACV8C,OAAO,EAAE,MAAM;IACfmE,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjClC,WAAW,EAAE;EACf,CAAC;EACDnB,cAAc,EAAE;IACd/B,eAAe,EAAE,OAAO;IACxBiB,YAAY,EAAE,MAAM;IACpBtO,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,MAAM;IAChBI,SAAS,EAAE,MAAM;IACjBzB,QAAQ,EAAE,QAAQ;IAClBpD,SAAS,EAAE,gCAAgC;IAC3CtM,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvBoH,MAAM,EAAE,MAAM;IACdxH,QAAQ,EAAE;EACZ,CAAC;EACDkE,WAAW,EAAE;IACXnN,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,eAAe;IAC/BM,OAAO,EAAE,qBAAqB;IAC9BkF,YAAY,EAAE;EAChB,CAAC;EACDhE,UAAU,EAAE;IACV3D,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBkH,MAAM,EAAE;EACV,CAAC;EACDpD,aAAa,EAAE;IACbxP,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdmO,YAAY,EAAE,KAAK;IACnB6D,MAAM,EAAE,MAAM;IACd9E,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,MAAM;IACbwG,MAAM,EAAE,SAAS;IACjB/P,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBqE,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE;EACX,CAAC;EACD5C,YAAY,EAAE;IACZpB,OAAO,EAAE,qBAAqB;IAC9BlM,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvB+G,GAAG,EAAE,MAAM;IACXiB,SAAS,EAAE,MAAM;IACjBzB,uBAAuB,EAAE,OAAO;IAChCuB,SAAS,EAAE;EACb,CAAC;EAED;EACAG,gBAAgB,EAAE;IAChBtR,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpB8G,GAAG,EAAE;EACP,CAAC;EACDmB,cAAc,EAAE;IACd9H,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBkH,MAAM,EAAE,CAAC;IACTI,SAAS,EAAE;EACb,CAAC;EACDW,iBAAiB,EAAE;IACjB/H,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbkH,MAAM,EAAE,CAAC;IACTI,SAAS,EAAE,QAAQ;IACnBY,UAAU,EAAE;EACd,CAAC;EACDlE,eAAe,EAAE;IACfvN,OAAO,EAAE,MAAM;IACfoQ,GAAG,EAAE,MAAM;IACXvS,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE;EACZ,CAAC;EACDvD,YAAY,EAAE;IACZqC,IAAI,EAAE,CAAC;IACP3D,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3B9E,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,MAAM;IACb2G,OAAO,EAAE;EACX,CAAC;EACDzC,kBAAkB,EAAE;IAClBvC,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,SAAS;IAChB0F,WAAW,EAAE,SAAS;IACtB3C,SAAS,EAAE;EACb,CAAC;EACDoB,eAAe,EAAE;IACf7P,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,OAAO;IACjB/Q,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvB+G,GAAG,EAAE;EACP,CAAC;EACDzC,WAAW,EAAE;IACXlE,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,MAAM;IACbsH,SAAS,EAAE,QAAQ;IACnB7Q,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBwE,GAAG,EAAE;EACP,CAAC;EACDxC,UAAU,EAAE;IACVnE,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChB2B,eAAe,EAAE,yBAAyB;IAC1CgB,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE;EAChB,CAAC;EACD4B,MAAM,EAAE;IACNlQ,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,KAAK;IACbmO,YAAY,EAAE,KAAK;IACnBuF,UAAU,EAAE,SAAS;IACrBxB,OAAO,EAAE,MAAM;IACfH,MAAM,EAAE,SAAS;IACjB4B,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACD5D,YAAY,EAAE;IACZhO,OAAO,EAAE,MAAM;IACf4L,cAAc,EAAE,eAAe;IAC/BnC,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbqH,SAAS,EAAE;EACb,CAAC;EACD3C,aAAa,EAAE;IACbjO,OAAO,EAAE,MAAM;IACfoQ,GAAG,EAAE,KAAK;IACVvS,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE;EACZ,CAAC;EACD7C,YAAY,EAAE;IACZ2B,IAAI,EAAE,CAAC;IACP3D,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,KAAK;IACnB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3B9E,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,MAAM;IACb2G,OAAO,EAAE;EACX,CAAC;EACD2B,cAAc,EAAE;IACdhU,KAAK,EAAE,MAAM;IACbkT,QAAQ,EAAE,OAAO;IACjB7E,OAAO,EAAE,WAAW;IACpBhB,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,SAAS;IAChB4C,YAAY,EAAE,MAAM;IACpB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACf5D,SAAS,EAAE;EACb,CAAC;EAED6B,gBAAgB,EAAE;IAChBlF,QAAQ,EAAE,UAAU;IACpB+C,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACP9C,KAAK,EAAE,CAAC;IACR+B,eAAe,EAAE,2BAA2B;IAC5CmF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCwB,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5B7F,OAAO,EAAE,MAAM;IACfiF,SAAS,EAAE,MAAM;IACjBnR,OAAO,EAAE,MAAM;IACfqJ,aAAa,EAAE,QAAQ;IACvBD,MAAM,EAAE,EAAE;IACVkD,SAAS,EAAE,iCAAiC;IAC5C0D,MAAM,EAAE,MAAM;IACdlE,SAAS,EAAE,eAAe;IAC1BmE,UAAU,EAAE,yBAAyB;IACrCP,QAAQ,EAAE,QAAQ;IAClBtB,WAAW,EAAE,MAAM;IACnB4D,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,QAAQ,EAAE;IACRlJ,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbI,KAAK,EAAE,MAAM;IACbwG,MAAM,EAAE,SAAS;IACjB3G,MAAM,EAAE,EAAE;IACVvL,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACdgC,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBO,YAAY,EAAE,KAAK;IACnBjB,eAAe,EAAE,oBAAoB;IACrC+E,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACfhE,OAAO,EAAE;EACX,CAAC;EACDwC,WAAW,EAAE;IACX1O,OAAO,EAAE,MAAM;IACf0J,YAAY,EAAE,MAAM;IACpBwB,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE,KAAK;IACdkE,GAAG,EAAE;EACP,CAAC;EACDzB,GAAG,EAAE;IACHkB,IAAI,EAAE,CAAC;IACPgB,SAAS,EAAE,QAAQ;IACnB3E,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,KAAK;IACnB1C,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBuG,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3B1G,KAAK,EAAE,MAAM;IACb2G,OAAO,EAAE,MAAM;IACfF,MAAM,EAAE,MAAM;IACd9E,eAAe,EAAE,aAAa;IAC9ByE,uBAAuB,EAAE,aAAa;IACtCvB,WAAW,EAAE;EACf,CAAC;EACD1U,SAAS,EAAE;IACTwR,eAAe,EAAE,SAAS;IAC1B3B,KAAK,EAAE,SAAS;IAChB+C,SAAS,EAAE;EACb,CAAC;EACDsC,aAAa,EAAE;IACb5O,OAAO,EAAE,MAAM;IACfoS,mBAAmB,EAAE,uCAAuC;IAC5DhC,GAAG,EAAE,MAAM;IACXe,SAAS,EAAE,oBAAoB;IAC/BE,SAAS,EAAE,MAAM;IACjBgB,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,cAAc;IAC9B3C,uBAAuB,EAAE;EAC3B,CAAC;EACDZ,WAAW,EAAE;IACX/F,QAAQ,EAAE,UAAU;IACpBpL,KAAK,EAAE,MAAM;IACb2U,WAAW,EAAE,KAAK;IAClBtH,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,MAAM;IACpBnM,OAAO,EAAE,MAAM;IACfsJ,UAAU,EAAE,QAAQ;IACpBsC,cAAc,EAAE,QAAQ;IACxBmE,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BN,QAAQ,EAAE,QAAQ;IAClBpD,SAAS,EAAE,+BAA+B;IAC1CJ,OAAO,EAAE,KAAK;IACdgE,OAAO,EAAE,MAAM;IACfP,uBAAuB,EAAE,aAAa;IACtCvB,WAAW,EAAE;EACf,CAAC;EACDe,YAAY,EAAE;IACZtR,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,MAAM;IACd6N,SAAS,EAAE,SAAS;IACpBM,YAAY,EAAE,KAAK;IACnBjB,eAAe,EAAE;EACnB,CAAC;EACDmE,YAAY,EAAE;IACZpG,QAAQ,EAAE,UAAU;IACpB+C,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACX9C,KAAK,EAAE,KAAK;IACZM,QAAQ,EAAE,KAAK;IACfF,KAAK,EAAE,MAAM;IACbsH,SAAS,EAAE,QAAQ;IACnB3F,eAAe,EAAE,2BAA2B;IAC5CiB,YAAY,EAAE,KAAK;IACnBD,OAAO,EAAE,SAAS;IAClBwD,QAAQ,EAAE;EACZ,CAAC;EACDJ,WAAW,EAAE;IACX7F,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjB4C,UAAU,EAAE,QAAQ;IACpBqG,YAAY,EAAE,UAAU;IACxB/C,QAAQ,EAAE,QAAQ;IAClBhG,YAAY,EAAE;EAChB,CAAC;EACD6F,WAAW,EAAE;IACX9F,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChB6C,UAAU,EAAE;EACd,CAAC;EACDqC,UAAU,EAAE;IACV5Q,KAAK,EAAE,MAAM;IACbG,MAAM,EAAE,KAAK;IACbkN,eAAe,EAAE,SAAS;IAC1BiB,YAAY,EAAE,KAAK;IACnBsE,MAAM,EAAE,aAAa;IACrBV,MAAM,EAAE,MAAM;IACd3B,WAAW,EAAE,MAAM;IACnB6D,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,eAAetZ,KAAK;AAAC,IAAA4W,EAAA;AAAAkD,YAAA,CAAAlD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}